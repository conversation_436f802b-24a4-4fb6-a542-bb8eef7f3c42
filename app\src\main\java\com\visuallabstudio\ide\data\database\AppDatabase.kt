package com.visuallabstudio.ide.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.visuallabstudio.ide.data.dao.*
import com.visuallabstudio.ide.data.entities.*

@Database(
    entities = [
        FileEntity::class,
        ProjectEntity::class,
        RecentFileEntity::class,
        UserSettingsEntity::class,
        CodeSnippetEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun fileDao(): FileDao
    abstract fun projectDao(): ProjectDao
    abstract fun recentFileDao(): RecentFileDao
    abstract fun userSettingsDao(): UserSettingsDao
    abstract fun codeSnippetDao(): CodeSnippetDao

    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "visual_lab_studio_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
