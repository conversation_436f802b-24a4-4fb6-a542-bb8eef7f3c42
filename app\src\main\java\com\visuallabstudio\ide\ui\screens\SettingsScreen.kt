package com.visuallabstudio.ide.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.visuallabstudio.ide.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: MainViewModel
) {
    val isDarkTheme by viewModel.isDarkTheme.collectAsState()
    val fontSize by viewModel.fontSize.collectAsState()
    val isAutoSaveEnabled by viewModel.isAutoSaveEnabled.collectAsState()
    val areLineNumbersEnabled by viewModel.areLineNumbersEnabled.collectAsState()
    val isSyntaxHighlightEnabled by viewModel.isSyntaxHighlightEnabled.collectAsState()
    val defaultLanguage by viewModel.defaultLanguage.collectAsState()
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Settings",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        // Theme Settings
        item {
            SettingsSection(title = "Appearance") {
                SettingsItem(
                    icon = Icons.Default.Palette,
                    title = "Dark Theme",
                    subtitle = "Use dark theme for the interface"
                ) {
                    Switch(
                        checked = isDarkTheme,
                        onCheckedChange = { viewModel.setDarkTheme(it) }
                    )
                }
            }
        }
        
        // Editor Settings
        item {
            SettingsSection(title = "Editor") {
                SettingsItem(
                    icon = Icons.Default.FormatSize,
                    title = "Font Size",
                    subtitle = "Adjust editor font size"
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        IconButton(
                            onClick = { if (fontSize > 10) viewModel.setFontSize(fontSize - 1) }
                        ) {
                            Icon(Icons.Default.Remove, contentDescription = "Decrease")
                        }
                        
                        Text(
                            text = fontSize.toString(),
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.width(32.dp)
                        )
                        
                        IconButton(
                            onClick = { if (fontSize < 24) viewModel.setFontSize(fontSize + 1) }
                        ) {
                            Icon(Icons.Default.Add, contentDescription = "Increase")
                        }
                    }
                }
                
                SettingsItem(
                    icon = Icons.Default.Numbers,
                    title = "Line Numbers",
                    subtitle = "Show line numbers in editor"
                ) {
                    Switch(
                        checked = areLineNumbersEnabled,
                        onCheckedChange = { viewModel.setLineNumbers(it) }
                    )
                }
                
                SettingsItem(
                    icon = Icons.Default.Highlight,
                    title = "Syntax Highlighting",
                    subtitle = "Enable syntax highlighting"
                ) {
                    Switch(
                        checked = isSyntaxHighlightEnabled,
                        onCheckedChange = { viewModel.setSyntaxHighlight(it) }
                    )
                }
                
                SettingsItem(
                    icon = Icons.Default.Code,
                    title = "Default Language",
                    subtitle = "Default language for new files"
                ) {
                    var expanded by remember { mutableStateOf(false) }
                    val languages = listOf("python", "javascript", "html", "css", "text")
                    
                    ExposedDropdownMenuBox(
                        expanded = expanded,
                        onExpandedChange = { expanded = !expanded }
                    ) {
                        OutlinedTextField(
                            value = defaultLanguage,
                            onValueChange = {},
                            readOnly = true,
                            trailingIcon = {
                                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                            },
                            modifier = Modifier
                                .menuAnchor()
                                .width(120.dp)
                        )
                        
                        ExposedDropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            languages.forEach { language ->
                                DropdownMenuItem(
                                    text = { Text(language.capitalize()) },
                                    onClick = {
                                        viewModel.setDefaultLanguage(language)
                                        expanded = false
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // File Settings
        item {
            SettingsSection(title = "Files") {
                SettingsItem(
                    icon = Icons.Default.Save,
                    title = "Auto Save",
                    subtitle = "Automatically save files when editing"
                ) {
                    Switch(
                        checked = isAutoSaveEnabled,
                        onCheckedChange = { viewModel.setAutoSave(it) }
                    )
                }
            }
        }
        
        // About Section
        item {
            SettingsSection(title = "About") {
                SettingsItem(
                    icon = Icons.Default.Info,
                    title = "Visual Lab Studio IDE",
                    subtitle = "Version 1.0.0"
                ) {}
                
                SettingsItem(
                    icon = Icons.Default.Code,
                    title = "Built with",
                    subtitle = "Kotlin • Jetpack Compose • Room • Chaquopy"
                ) {}
            }
        }
    }
}

@Composable
private fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            content()
        }
    }
}

@Composable
private fun SettingsItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    action: @Composable () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        action()
    }
}
