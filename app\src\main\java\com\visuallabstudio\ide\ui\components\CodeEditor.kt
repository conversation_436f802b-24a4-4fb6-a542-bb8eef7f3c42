package com.visuallabstudio.ide.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.visuallabstudio.ide.ui.theme.*

@Composable
fun CodeEditor(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    language: String = "python",
    fontSize: Int = 14,
    showLineNumbers: Boolean = true,
    modifier: Modifier = Modifier
) {
    val syntaxHighlighter = remember { SyntaxHighlighter() }
    val lines = remember(value.text) { value.text.split('\n') }
    val listState = rememberLazyListState()
    
    Row(
        modifier = modifier
            .background(EditorBackground)
            .border(1.dp, DarkSurfaceVariant)
    ) {
        // Line numbers
        if (showLineNumbers) {
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .width(50.dp)
                    .background(DarkSurface)
                    .padding(horizontal = 8.dp, vertical = 4.dp)
            ) {
                itemsIndexed(lines) { index, _ ->
                    Text(
                        text = "${index + 1}",
                        style = TextStyle(
                            fontFamily = CodeFontFamily,
                            fontSize = fontSize.sp,
                            color = EditorLineNumber
                        ),
                        modifier = Modifier.padding(vertical = 2.dp)
                    )
                }
            }
            
            // Divider
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .fillMaxHeight()
                    .background(DarkSurfaceVariant)
            )
        }
        
        // Code editor
        LazyColumn(
            state = listState,
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 8.dp, vertical = 4.dp)
        ) {
            itemsIndexed(lines) { index, line ->
                CodeLine(
                    line = line,
                    lineNumber = index,
                    language = language,
                    fontSize = fontSize,
                    syntaxHighlighter = syntaxHighlighter,
                    onLineChange = { newLine ->
                        val newLines = lines.toMutableList()
                        newLines[index] = newLine
                        val newText = newLines.joinToString("\n")
                        onValueChange(value.copy(text = newText))
                    }
                )
            }
        }
    }
}

@Composable
private fun CodeLine(
    line: String,
    lineNumber: Int,
    language: String,
    fontSize: Int,
    syntaxHighlighter: SyntaxHighlighter,
    onLineChange: (String) -> Unit
) {
    var textFieldValue by remember(line) { 
        mutableStateOf(TextFieldValue(line))
    }
    
    val highlightedText = remember(line, language) {
        syntaxHighlighter.highlight(line, language)
    }
    
    BasicTextField(
        value = textFieldValue,
        onValueChange = { newValue ->
            textFieldValue = newValue
            onLineChange(newValue.text)
        },
        textStyle = TextStyle(
            fontFamily = CodeFontFamily,
            fontSize = fontSize.sp,
            color = DarkOnSurface
        ),
        cursorBrush = SolidColor(EditorCursor),
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Text
        ),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
    ) { innerTextField ->
        Box {
            // Highlighted text (background)
            if (textFieldValue.text.isEmpty()) {
                Text(
                    text = highlightedText,
                    style = TextStyle(
                        fontFamily = CodeFontFamily,
                        fontSize = fontSize.sp
                    )
                )
            }
            
            // Actual text field (foreground, transparent when not focused)
            innerTextField()
        }
    }
}

@Composable
fun SimpleCodeEditor(
    value: String,
    onValueChange: (String) -> Unit,
    language: String = "python",
    fontSize: Int = 14,
    showLineNumbers: Boolean = true,
    modifier: Modifier = Modifier
) {
    var textFieldValue by remember(value) {
        mutableStateOf(TextFieldValue(value))
    }
    
    LaunchedEffect(value) {
        if (textFieldValue.text != value) {
            textFieldValue = TextFieldValue(value)
        }
    }
    
    val syntaxHighlighter = remember { SyntaxHighlighter() }
    val highlightedText = remember(value, language) {
        syntaxHighlighter.highlight(value, language)
    }
    
    Row(
        modifier = modifier
            .background(EditorBackground)
            .border(1.dp, DarkSurfaceVariant)
    ) {
        // Line numbers
        if (showLineNumbers) {
            val lines = value.split('\n')
            Column(
                modifier = Modifier
                    .width(50.dp)
                    .background(DarkSurface)
                    .padding(horizontal = 8.dp, vertical = 8.dp)
            ) {
                lines.forEachIndexed { index, _ ->
                    Text(
                        text = "${index + 1}",
                        style = TextStyle(
                            fontFamily = CodeFontFamily,
                            fontSize = fontSize.sp,
                            color = EditorLineNumber
                        ),
                        modifier = Modifier.padding(vertical = 1.dp)
                    )
                }
            }
            
            // Divider
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .fillMaxHeight()
                    .background(DarkSurfaceVariant)
            )
        }
        
        // Code editor
        Box(
            modifier = Modifier
                .weight(1f)
                .padding(8.dp)
        ) {
            BasicTextField(
                value = textFieldValue,
                onValueChange = { newValue ->
                    textFieldValue = newValue
                    onValueChange(newValue.text)
                },
                textStyle = TextStyle(
                    fontFamily = CodeFontFamily,
                    fontSize = fontSize.sp,
                    color = DarkOnSurface,
                    lineHeight = (fontSize + 4).sp
                ),
                cursorBrush = SolidColor(EditorCursor),
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Text
                ),
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}
