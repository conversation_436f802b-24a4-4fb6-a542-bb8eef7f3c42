package com.visuallabstudio.ide.ui.components

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import com.visuallabstudio.ide.ui.theme.SyntaxColors

class SyntaxHighlighter {
    
    fun highlight(code: String, language: String): AnnotatedString {
        return when (language.lowercase()) {
            "python" -> highlightPython(code)
            "javascript", "js" -> highlightJavaScript(code)
            "html" -> highlightHtml(code)
            "css" -> highlightCss(code)
            "json" -> highlightJson(code)
            else -> buildAnnotatedString { append(code) }
        }
    }
    
    private fun highlightPython(code: String): AnnotatedString {
        return buildAnnotatedString {
            val keywords = setOf(
                "def", "class", "if", "else", "elif", "for", "while", "try", "except", 
                "finally", "with", "as", "import", "from", "return", "yield", "lambda",
                "and", "or", "not", "in", "is", "True", "False", "None", "pass", "break", "continue"
            )
            
            val builtins = setOf(
                "print", "len", "str", "int", "float", "list", "dict", "tuple", "set",
                "range", "enumerate", "zip", "map", "filter", "sorted", "reversed"
            )
            
            highlightCode(code, keywords, builtins)
        }
    }
    
    private fun highlightJavaScript(code: String): AnnotatedString {
        return buildAnnotatedString {
            val keywords = setOf(
                "function", "var", "let", "const", "if", "else", "for", "while", "do",
                "switch", "case", "default", "break", "continue", "return", "try", "catch",
                "finally", "throw", "new", "this", "typeof", "instanceof", "true", "false",
                "null", "undefined", "class", "extends", "super", "static", "async", "await"
            )
            
            val builtins = setOf(
                "console", "document", "window", "Array", "Object", "String", "Number",
                "Boolean", "Date", "Math", "JSON", "Promise", "setTimeout", "setInterval"
            )
            
            highlightCode(code, keywords, builtins)
        }
    }
    
    private fun highlightHtml(code: String): AnnotatedString {
        return buildAnnotatedString {
            var i = 0
            while (i < code.length) {
                when {
                    code[i] == '<' -> {
                        val tagEnd = code.indexOf('>', i)
                        if (tagEnd != -1) {
                            val tagContent = code.substring(i, tagEnd + 1)
                            highlightHtmlTag(tagContent)
                            i = tagEnd + 1
                        } else {
                            append(code[i])
                            i++
                        }
                    }
                    else -> {
                        append(code[i])
                        i++
                    }
                }
            }
        }
    }
    
    private fun AnnotatedString.Builder.highlightHtmlTag(tag: String) {
        var i = 0
        while (i < tag.length) {
            when {
                tag[i] == '<' || tag[i] == '>' -> {
                    withStyle(SpanStyle(color = SyntaxColors.Tag)) {
                        append(tag[i])
                    }
                    i++
                }
                tag[i].isLetter() -> {
                    val wordStart = i
                    while (i < tag.length && (tag[i].isLetterOrDigit() || tag[i] == '-')) {
                        i++
                    }
                    val word = tag.substring(wordStart, i)
                    withStyle(SpanStyle(color = SyntaxColors.Tag)) {
                        append(word)
                    }
                }
                tag[i] == '=' -> {
                    withStyle(SpanStyle(color = SyntaxColors.Operator)) {
                        append(tag[i])
                    }
                    i++
                }
                tag[i] == '"' || tag[i] == '\'' -> {
                    val quote = tag[i]
                    val stringStart = i
                    i++
                    while (i < tag.length && tag[i] != quote) {
                        i++
                    }
                    if (i < tag.length) i++ // Include closing quote
                    val string = tag.substring(stringStart, i)
                    withStyle(SpanStyle(color = SyntaxColors.String)) {
                        append(string)
                    }
                }
                else -> {
                    append(tag[i])
                    i++
                }
            }
        }
    }
    
    private fun highlightCss(code: String): AnnotatedString {
        return buildAnnotatedString {
            val properties = setOf(
                "color", "background", "font-size", "margin", "padding", "border",
                "width", "height", "display", "position", "top", "left", "right", "bottom",
                "text-align", "font-family", "font-weight", "line-height", "opacity"
            )
            
            highlightCode(code, emptySet(), properties)
        }
    }
    
    private fun highlightJson(code: String): AnnotatedString {
        return buildAnnotatedString {
            var i = 0
            while (i < code.length) {
                when {
                    code[i] == '"' -> {
                        val stringStart = i
                        i++
                        while (i < code.length && code[i] != '"') {
                            if (code[i] == '\\') i++ // Skip escaped character
                            i++
                        }
                        if (i < code.length) i++ // Include closing quote
                        val string = code.substring(stringStart, i)
                        withStyle(SpanStyle(color = SyntaxColors.String)) {
                            append(string)
                        }
                    }
                    code[i].isDigit() || (code[i] == '-' && i + 1 < code.length && code[i + 1].isDigit()) -> {
                        val numberStart = i
                        if (code[i] == '-') i++
                        while (i < code.length && (code[i].isDigit() || code[i] == '.')) {
                            i++
                        }
                        val number = code.substring(numberStart, i)
                        withStyle(SpanStyle(color = SyntaxColors.Number)) {
                            append(number)
                        }
                    }
                    code.substring(i).startsWith("true") || code.substring(i).startsWith("false") || code.substring(i).startsWith("null") -> {
                        val word = when {
                            code.substring(i).startsWith("true") -> "true"
                            code.substring(i).startsWith("false") -> "false"
                            else -> "null"
                        }
                        withStyle(SpanStyle(color = SyntaxColors.Constant)) {
                            append(word)
                        }
                        i += word.length
                    }
                    else -> {
                        append(code[i])
                        i++
                    }
                }
            }
        }
    }
    
    private fun AnnotatedString.Builder.highlightCode(
        code: String,
        keywords: Set<String>,
        builtins: Set<String>
    ) {
        var i = 0
        while (i < code.length) {
            when {
                code[i] == '#' -> {
                    // Single line comment
                    val lineEnd = code.indexOf('\n', i).let { if (it == -1) code.length else it }
                    val comment = code.substring(i, lineEnd)
                    withStyle(SpanStyle(color = SyntaxColors.Comment)) {
                        append(comment)
                    }
                    i = lineEnd
                }
                code[i] == '"' || code[i] == '\'' -> {
                    // String literal
                    val quote = code[i]
                    val stringStart = i
                    i++
                    while (i < code.length && code[i] != quote) {
                        if (code[i] == '\\') i++ // Skip escaped character
                        i++
                    }
                    if (i < code.length) i++ // Include closing quote
                    val string = code.substring(stringStart, i)
                    withStyle(SpanStyle(color = SyntaxColors.String)) {
                        append(string)
                    }
                }
                code[i].isDigit() -> {
                    // Number
                    val numberStart = i
                    while (i < code.length && (code[i].isDigit() || code[i] == '.')) {
                        i++
                    }
                    val number = code.substring(numberStart, i)
                    withStyle(SpanStyle(color = SyntaxColors.Number)) {
                        append(number)
                    }
                }
                code[i].isLetter() || code[i] == '_' -> {
                    // Identifier
                    val wordStart = i
                    while (i < code.length && (code[i].isLetterOrDigit() || code[i] == '_')) {
                        i++
                    }
                    val word = code.substring(wordStart, i)
                    val color = when {
                        word in keywords -> SyntaxColors.Keyword
                        word in builtins -> SyntaxColors.Function
                        else -> SyntaxColors.Variable
                    }
                    withStyle(SpanStyle(color = color)) {
                        append(word)
                    }
                }
                code[i] in "+-*/=<>!&|" -> {
                    // Operators
                    withStyle(SpanStyle(color = SyntaxColors.Operator)) {
                        append(code[i])
                    }
                    i++
                }
                else -> {
                    append(code[i])
                    i++
                }
            }
        }
    }
}
