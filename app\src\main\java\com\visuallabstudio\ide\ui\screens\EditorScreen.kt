package com.visuallabstudio.ide.ui.screens

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.visuallabstudio.ide.ui.components.SimpleCodeEditor
import com.visuallabstudio.ide.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditorScreen(
    viewModel: MainViewModel
) {
    val currentFile by viewModel.currentFile.collectAsState()
    val openFiles by viewModel.openFiles.collectAsState()
    var codeContent by remember { mutableStateOf("") }
    
    // File picker launcher
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri: Uri? ->
        uri?.let { viewModel.openFile(it) }
    }
    
    // File saver launcher
    val fileSaverLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.CreateDocument("text/*")
    ) { uri: Uri? ->
        uri?.let { 
            currentFile?.let { file ->
                viewModel.saveFile(file.copy(content = codeContent), it)
            }
        }
    }
    
    // Update code content when current file changes
    LaunchedEffect(currentFile) {
        currentFile?.let {
            codeContent = it.content
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // File tabs
        if (openFiles.isNotEmpty()) {
            LazyRow(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(openFiles) { file ->
                    FileTab(
                        fileName = file.name,
                        isSelected = file.id == currentFile?.id,
                        onSelect = { viewModel.selectFile(file.id) },
                        onClose = { viewModel.closeFile(file.id) }
                    )
                }
            }
        }
        
        // Toolbar
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(
                onClick = { viewModel.createNewFile() }
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("New")
            }
            
            Button(
                onClick = { 
                    filePickerLauncher.launch(arrayOf("text/*"))
                }
            ) {
                Icon(Icons.Default.FolderOpen, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Open")
            }
            
            Button(
                onClick = {
                    currentFile?.let { file ->
                        if (file.path.isNotEmpty()) {
                            viewModel.saveFile(file.copy(content = codeContent))
                        } else {
                            fileSaverLauncher.launch(file.name)
                        }
                    }
                },
                enabled = currentFile != null
            ) {
                Icon(Icons.Default.Save, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Save")
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            Button(
                onClick = {
                    currentFile?.let { file ->
                        viewModel.executeCode(file.content, file.language)
                    }
                },
                enabled = currentFile != null,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(Icons.Default.PlayArrow, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Run")
            }
        }
        
        // Code Editor
        if (currentFile != null) {
            SimpleCodeEditor(
                value = codeContent,
                onValueChange = { newContent ->
                    codeContent = newContent
                    currentFile?.let { file ->
                        viewModel.updateFileContent(file.id, newContent)
                    }
                },
                language = currentFile?.language ?: "python",
                modifier = Modifier.fillMaxSize()
            )
        } else {
            // Welcome screen
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        Icons.Default.Code,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        "Welcome to Visual Lab Studio IDE",
                        style = MaterialTheme.typography.headlineSmall
                    )
                    Text(
                        "Create a new file or open an existing one to start coding",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Button(
                            onClick = { viewModel.createNewFile() }
                        ) {
                            Text("New File")
                        }
                        
                        OutlinedButton(
                            onClick = { 
                                filePickerLauncher.launch(arrayOf("text/*"))
                            }
                        ) {
                            Text("Open File")
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun FileTab(
    fileName: String,
    isSelected: Boolean,
    onSelect: () -> Unit,
    onClose: () -> Unit
) {
    Card(
        modifier = Modifier.height(40.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        onClick = onSelect
    ) {
        Row(
            modifier = Modifier
                .padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = fileName,
                style = MaterialTheme.typography.bodySmall,
                maxLines = 1
            )
            
            IconButton(
                onClick = onClose,
                modifier = Modifier.size(16.dp)
            ) {
                Icon(
                    Icons.Default.Close,
                    contentDescription = "Close",
                    modifier = Modifier.size(12.dp)
                )
            }
        }
    }
}
