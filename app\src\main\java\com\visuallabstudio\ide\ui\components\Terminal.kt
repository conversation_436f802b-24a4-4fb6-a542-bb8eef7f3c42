package com.visuallabstudio.ide.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.visuallabstudio.ide.core.terminal.TerminalLine
import com.visuallabstudio.ide.core.terminal.TerminalLineType
import com.visuallabstudio.ide.core.terminal.TerminalManager
import com.visuallabstudio.ide.ui.theme.*

@Composable
fun Terminal(
    terminalManager: TerminalManager,
    modifier: Modifier = Modifier
) {
    val output by terminalManager.output.collectAsState()
    val isRunning by terminalManager.isRunning.collectAsState()
    val listState = rememberLazyListState()
    var inputText by remember { mutableStateOf(TextFieldValue("")) }
    
    // Auto-scroll to bottom when new output is added
    LaunchedEffect(output.size) {
        if (output.isNotEmpty()) {
            listState.animateScrollToItem(output.size - 1)
        }
    }
    
    Column(
        modifier = modifier
            .background(TerminalBackground)
            .border(1.dp, DarkSurfaceVariant)
    ) {
        // Terminal header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(DarkSurface)
                .padding(8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Terminal",
                style = MaterialTheme.typography.titleSmall,
                color = DarkOnSurface,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                if (isRunning) {
                    IconButton(
                        onClick = { terminalManager.killCurrentProcess() }
                    ) {
                        Icon(
                            Icons.Default.Stop,
                            contentDescription = "Stop Process",
                            tint = TerminalError
                        )
                    }
                }
                
                IconButton(
                    onClick = { terminalManager.executeCommand("clear") }
                ) {
                    Icon(
                        Icons.Default.Clear,
                        contentDescription = "Clear Terminal",
                        tint = DarkOnSurface
                    )
                }
            }
        }
        
        // Terminal output
        LazyColumn(
            state = listState,
            modifier = Modifier
                .weight(1f)
                .padding(8.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            items(output) { line ->
                TerminalLineItem(line = line)
            }
        }
        
        // Terminal input
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(DarkSurface)
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$ ",
                style = TextStyle(
                    fontFamily = CodeFontFamily,
                    fontSize = 14.sp,
                    color = TerminalPrompt
                ),
                modifier = Modifier.padding(end = 4.dp)
            )
            
            BasicTextField(
                value = inputText,
                onValueChange = { inputText = it },
                enabled = !isRunning,
                textStyle = TextStyle(
                    fontFamily = CodeFontFamily,
                    fontSize = 14.sp,
                    color = TerminalText
                ),
                cursorBrush = SolidColor(TerminalPrompt),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Done
                ),
                keyboardActions = KeyboardActions(
                    onDone = {
                        if (inputText.text.isNotBlank()) {
                            terminalManager.executeCommand(inputText.text)
                            inputText = TextFieldValue("")
                        }
                    }
                ),
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun TerminalLineItem(line: TerminalLine) {
    val color = when (line.type) {
        TerminalLineType.INPUT -> TerminalPrompt
        TerminalLineType.OUTPUT -> TerminalText
        TerminalLineType.ERROR -> TerminalError
        TerminalLineType.WARNING -> TerminalWarning
        TerminalLineType.INFO -> CyanAccent
        TerminalLineType.PROMPT -> TerminalPrompt
    }
    
    val fontWeight = when (line.type) {
        TerminalLineType.INPUT -> FontWeight.Bold
        TerminalLineType.ERROR -> FontWeight.Bold
        TerminalLineType.WARNING -> FontWeight.Bold
        else -> FontWeight.Normal
    }
    
    Text(
        text = line.text,
        style = TextStyle(
            fontFamily = CodeFontFamily,
            fontSize = 13.sp,
            color = color,
            fontWeight = fontWeight
        ),
        modifier = Modifier.fillMaxWidth()
    )
}
