package com.visuallabstudio.ide.core.execution

import android.content.Context
import com.chaquo.python.Python
import com.chaquo.python.android.AndroidPlatform
import kotlinx.coroutines.*
import java.io.StringWriter
import java.io.PrintWriter

class PythonExecutor(private val context: Context) {
    
    private var python: Python? = null
    
    init {
        initializePython()
    }
    
    private fun initializePython() {
        try {
            if (!Python.isStarted()) {
                Python.start(AndroidPlatform(context))
            }
            python = Python.getInstance()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    suspend fun executeCode(
        code: String,
        onOutput: (String) -> Unit,
        onError: (String) -> Unit,
        onComplete: () -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            val py = python ?: run {
                onError("Python not initialized")
                onComplete()
                return@withContext
            }
            
            // Capture stdout and stderr
            val outputCapture = StringWriter()
            val errorCapture = StringWriter()
            
            // Create a custom output handler
            val outputHandler = py.getModule("sys")
            val originalStdout = outputHandler["stdout"]
            val originalStderr = outputHandler["stderr"]
            
            try {
                // Redirect output
                val ioModule = py.getModule("io")
                val stringIO = ioModule.callAttr("StringIO")
                outputHandler["stdout"] = stringIO
                
                val errorStringIO = ioModule.callAttr("StringIO")
                outputHandler["stderr"] = errorStringIO
                
                // Execute the code
                py.exec(code)
                
                // Get the output
                val output = stringIO.callAttr("getvalue").toString()
                val error = errorStringIO.callAttr("getvalue").toString()
                
                // Send output to callback
                if (output.isNotEmpty()) {
                    onOutput(output)
                }
                
                if (error.isNotEmpty()) {
                    onError(error)
                }
                
            } catch (e: Exception) {
                onError("Python execution error: ${e.message}")
            } finally {
                // Restore original stdout/stderr
                try {
                    outputHandler["stdout"] = originalStdout
                    outputHandler["stderr"] = originalStderr
                } catch (e: Exception) {
                    // Ignore restoration errors
                }
                onComplete()
            }
            
        } catch (e: Exception) {
            onError("Failed to execute Python code: ${e.message}")
            onComplete()
        }
    }
    
    suspend fun executeFile(
        filePath: String,
        onOutput: (String) -> Unit,
        onError: (String) -> Unit,
        onComplete: () -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            val py = python ?: run {
                onError("Python not initialized")
                onComplete()
                return@withContext
            }
            
            // Read file content
            val file = java.io.File(filePath)
            if (!file.exists()) {
                onError("File not found: $filePath")
                onComplete()
                return@withContext
            }
            
            val code = file.readText()
            executeCode(code, onOutput, onError, onComplete)
            
        } catch (e: Exception) {
            onError("Failed to execute Python file: ${e.message}")
            onComplete()
        }
    }
    
    suspend fun evaluateExpression(
        expression: String,
        onResult: (String) -> Unit,
        onError: (String) -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            val py = python ?: run {
                onError("Python not initialized")
                return@withContext
            }
            
            val result = py.eval(expression)
            onResult(result.toString())
            
        } catch (e: Exception) {
            onError("Python evaluation error: ${e.message}")
        }
    }
    
    fun getAvailableModules(): List<String> {
        return try {
            val py = python ?: return emptyList()
            val sysModule = py.getModule("sys")
            val modules = sysModule["modules"]
            modules.asMap().keys.map { it.toString() }.sorted()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    fun installPackage(packageName: String, onResult: (Boolean, String) -> Unit) {
        // Note: Chaquopy doesn't support runtime pip installation
        // Packages must be pre-installed in build.gradle
        onResult(false, "Package installation not supported. Add packages to build.gradle")
    }
    
    fun getPythonVersion(): String {
        return try {
            val py = python ?: return "Unknown"
            val sysModule = py.getModule("sys")
            sysModule["version"].toString()
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    fun cleanup() {
        // Python cleanup is handled automatically by Chaquopy
    }
}
