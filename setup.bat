@echo off
echo ========================================
echo Visual Lab Studio IDE Mobile - Setup
echo ========================================
echo.

echo [1/4] Verificando ambiente...

REM Verificar se Android Studio está instalado
if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo ✅ Android SDK encontrado
    set SDK_PATH=%LOCALAPPDATA%\Android\Sdk
) else (
    echo ❌ Android SDK não encontrado
    echo Por favor, instale o Android Studio primeiro
    pause
    exit /b 1
)

echo [2/4] Configurando local.properties...

REM Criar local.properties com o caminho correto
echo sdk.dir=%SDK_PATH:\=\\% > local.properties
echo ✅ local.properties configurado

echo [3/4] Verificando Java...
java -version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Java encontrado
) else (
    echo ❌ Java não encontrado
    echo Por favor, instale o JDK 8 ou superior
    pause
    exit /b 1
)

echo [4/4] Preparando projeto...
echo ✅ Projeto pronto para abrir no Android Studio

echo.
echo ========================================
echo PRÓXIMOS PASSOS:
echo ========================================
echo 1. Abra o Android Studio
echo 2. File → Open → Selecione esta pasta
echo 3. Aguarde a sincronização do Gradle
echo 4. Run → Run 'app'
echo.
echo ✅ Setup concluído com sucesso!
echo ========================================

pause
