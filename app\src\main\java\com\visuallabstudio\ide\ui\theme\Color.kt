package com.visuallabstudio.ide.ui.theme

import androidx.compose.ui.graphics.Color

// Dark Theme Colors (Primary)
val DarkBackground = Color(0xFF0D1117)
val DarkSurface = Color(0xFF161B22)
val DarkSurfaceVariant = Color(0xFF21262D)
val DarkOnBackground = Color(0xFFE6EDF3)
val DarkOnSurface = Color(0xFFE6EDF3)

// Accent Colors
val CyanAccent = Color(0xFF00D4FF)
val PurpleAccent = Color(0xFF8B5CF6)
val NeonGreen = Color(0xFF39FF14)
val NeonBlue = Color(0xFF00FFFF)

// Editor Colors
val EditorBackground = Color(0xFF0D1117)
val EditorLineNumber = Color(0xFF7D8590)
val EditorSelection = Color(0xFF264F78)
val EditorCursor = Color(0xFF00D4FF)

// Syntax Highlighting Colors
object SyntaxColors {
    val Keyword = Color(0xFF8B5CF6) // Purple
    val String = Color(0xFF39FF14) // Neon Green
    val Comment = Color(0xFF7D8590) // Gray
    val Number = Color(0xFF00D4FF) // Cyan
    val Function = Color(0xFFFFD700) // Gold
    val Variable = Color(0xFFE6EDF3) // White
    val Operator = Color(0xFFFF6B6B) // Red
    val Type = Color(0xFF4ECDC4) // Teal
    val Constant = Color(0xFFFF8C42) // Orange
    val Tag = Color(0xFF8B5CF6) // Purple (HTML tags)
    val Attribute = Color(0xFF00D4FF) // Cyan (HTML attributes)
    val Value = Color(0xFF39FF14) // Green (HTML attribute values)
}

// Terminal Colors
val TerminalBackground = Color(0xFF000000)
val TerminalText = Color(0xFF00FF00)
val TerminalPrompt = Color(0xFF00D4FF)
val TerminalError = Color(0xFFFF4444)
val TerminalWarning = Color(0xFFFFAA00)

// Light Theme Colors (Optional)
val LightBackground = Color(0xFFFFFFFF)
val LightSurface = Color(0xFFF6F8FA)
val LightSurfaceVariant = Color(0xFFEAEEF2)
val LightOnBackground = Color(0xFF24292F)
val LightOnSurface = Color(0xFF24292F)
