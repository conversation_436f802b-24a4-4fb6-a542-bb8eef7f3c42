package com.visuallabstudio.ide.core.terminal

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.*

class TerminalManager(private val context: Context) {
    private val _output = MutableStateFlow<List<TerminalLine>>(emptyList())
    val output: StateFlow<List<TerminalLine>> = _output.asStateFlow()
    
    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning.asStateFlow()
    
    private var currentProcess: Process? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    init {
        addLine("Visual Lab Studio Terminal", TerminalLineType.INFO)
        addLine("Type 'help' for available commands", TerminalLineType.INFO)
        addPrompt()
    }
    
    fun executeCommand(command: String) {
        if (command.isBlank()) {
            addPrompt()
            return
        }
        
        addLine("$ $command", TerminalLineType.INPUT)
        
        when {
            command.trim() == "clear" -> {
                clearTerminal()
                return
            }
            command.trim() == "help" -> {
                showHelp()
                return
            }
            command.startsWith("python ") || command.startsWith("py ") -> {
                executePythonCommand(command)
                return
            }
            command.startsWith("node ") || command.startsWith("js ") -> {
                executeJavaScriptCommand(command)
                return
            }
            else -> {
                executeSystemCommand(command)
            }
        }
    }
    
    private fun executeSystemCommand(command: String) {
        scope.launch {
            try {
                _isRunning.value = true
                
                val processBuilder = ProcessBuilder()
                processBuilder.command("sh", "-c", command)
                processBuilder.directory(context.filesDir)
                
                val process = processBuilder.start()
                currentProcess = process
                
                // Read output
                val outputReader = BufferedReader(InputStreamReader(process.inputStream))
                val errorReader = BufferedReader(InputStreamReader(process.errorStream))
                
                // Launch coroutines to read both streams
                val outputJob = launch {
                    outputReader.useLines { lines ->
                        lines.forEach { line ->
                            addLine(line, TerminalLineType.OUTPUT)
                        }
                    }
                }
                
                val errorJob = launch {
                    errorReader.useLines { lines ->
                        lines.forEach { line ->
                            addLine(line, TerminalLineType.ERROR)
                        }
                    }
                }
                
                // Wait for process to complete
                val exitCode = process.waitFor()
                
                // Wait for output reading to complete
                outputJob.join()
                errorJob.join()
                
                if (exitCode != 0) {
                    addLine("Process exited with code: $exitCode", TerminalLineType.ERROR)
                }
                
            } catch (e: Exception) {
                addLine("Error: ${e.message}", TerminalLineType.ERROR)
            } finally {
                _isRunning.value = false
                currentProcess = null
                addPrompt()
            }
        }
    }
    
    private fun executePythonCommand(command: String) {
        // This will be implemented with Chaquopy integration
        addLine("Python execution will be implemented with Chaquopy", TerminalLineType.INFO)
        addPrompt()
    }
    
    private fun executeJavaScriptCommand(command: String) {
        // This will be implemented with Rhino/QuickJS integration
        addLine("JavaScript execution will be implemented with Rhino", TerminalLineType.INFO)
        addPrompt()
    }
    
    private fun showHelp() {
        val helpText = listOf(
            "Available commands:",
            "  clear          - Clear terminal",
            "  help           - Show this help",
            "  python <file>  - Execute Python file",
            "  js <file>      - Execute JavaScript file",
            "  ls             - List files",
            "  pwd            - Show current directory",
            "  cat <file>     - Show file content",
            "  echo <text>    - Print text"
        )
        
        helpText.forEach { line ->
            addLine(line, TerminalLineType.INFO)
        }
        addPrompt()
    }
    
    private fun clearTerminal() {
        _output.value = emptyList()
        addLine("Visual Lab Studio Terminal", TerminalLineType.INFO)
        addPrompt()
    }
    
    private fun addLine(text: String, type: TerminalLineType) {
        val currentOutput = _output.value.toMutableList()
        currentOutput.add(TerminalLine(text, type, System.currentTimeMillis()))
        _output.value = currentOutput
    }
    
    private fun addPrompt() {
        addLine("$ ", TerminalLineType.PROMPT)
    }
    
    fun killCurrentProcess() {
        currentProcess?.destroyForcibly()
        currentProcess = null
        _isRunning.value = false
        addLine("Process terminated", TerminalLineType.WARNING)
        addPrompt()
    }
    
    fun cleanup() {
        killCurrentProcess()
        scope.cancel()
    }
}

data class TerminalLine(
    val text: String,
    val type: TerminalLineType,
    val timestamp: Long
)

enum class TerminalLineType {
    INPUT,      // User input
    OUTPUT,     // Command output
    ERROR,      // Error messages
    WARNING,    // Warning messages
    INFO,       // Info messages
    PROMPT      // Terminal prompt
}
