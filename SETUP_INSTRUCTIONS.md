# 🚀 Visual Lab Studio IDE Mobile - Instruções de Setup

## 📋 Pré-requisitos

Você já tem instalado:
- ✅ Android Studio
- ✅ Java/Kotlin extension no VS Code
- ✅ Emulador Android configurado

## 🛠️ Configuração do Ambiente

### 1. Configurar Android SDK Path

Edite o arquivo `local.properties` e ajuste o caminho do seu Android SDK:

```properties
# Windows (exemplo)
sdk.dir=C\:\\Users\\SeuUsuario\\AppData\\Local\\Android\\Sdk

# macOS (exemplo)  
sdk.dir=/Users/<USER>/Library/Android/sdk

# Linux (exemplo)
sdk.dir=/home/<USER>/Android/Sdk
```

### 2. Abrir no Android Studio

1. **Abra o Android Studio**
2. **File → Open** 
3. **Selecione a pasta do projeto**: `Visual Lab Studio Mobile`
4. **Aguarde a sincronização do Gradle** (primeira vez pode demorar)

### 3. Configurar Emulador

1. **Tools → AVD Manager**
2. **Create Virtual Device**
3. **Escolha um dispositivo** (ex: Pixel 6)
4. **API Level 24+** (Android 7.0+)
5. **Finish** e **Start** o emulador

## 🔧 Build e Execução

### Opção 1: Android Studio (Recomendado)

1. **Aguarde a sincronização do Gradle terminar**
2. **Build → Make Project** (Ctrl+F9)
3. **Run → Run 'app'** (Shift+F10)
4. **Selecione o emulador** na lista de dispositivos

### Opção 2: Linha de Comando

```bash
# No diretório do projeto
cd "Visual Lab Studio Mobile"

# Build debug
./gradlew assembleDebug

# Instalar no dispositivo/emulador
./gradlew installDebug
```

## 🐛 Solução de Problemas Comuns

### Erro: "SDK location not found"
- ✅ Verifique o caminho no `local.properties`
- ✅ Certifique-se que o Android SDK está instalado

### Erro: "Gradle sync failed"
- ✅ **File → Invalidate Caches and Restart**
- ✅ Verifique conexão com internet
- ✅ **Build → Clean Project**

### Erro: "Chaquopy not found"
- ✅ Aguarde o download das dependências
- ✅ Verifique se tem internet para baixar o Chaquopy

### Erro de compilação Kotlin
- ✅ **Build → Clean Project**
- ✅ **Build → Rebuild Project**
- ✅ Verifique se o Kotlin plugin está ativo

## 📱 Testando a IDE

### Funcionalidades para Testar:

1. **📝 Editor**
   - Criar novo arquivo Python
   - Syntax highlighting funcionando
   - Salvar arquivo

2. **⚡ Terminal**
   - Comandos básicos: `ls`, `pwd`, `echo`
   - Clear terminal

3. **🐍 Python**
   - Executar código Python simples
   - Ver output no terminal

4. **🟨 JavaScript**
   - Executar código JS
   - Console.log funcionando

5. **📁 Explorer**
   - Ver arquivos recentes
   - Abrir arquivos salvos

6. **⚙️ Settings**
   - Mudar tema
   - Ajustar font size

## 🎯 Arquivos de Exemplo

Use os arquivos em `app/src/main/assets/examples/`:
- `hello_world.py` - Exemplo Python
- `hello_world.js` - Exemplo JavaScript

## 📞 Suporte

Se encontrar problemas:

1. **Verifique os logs** no Android Studio (Logcat)
2. **Clean e Rebuild** o projeto
3. **Restart** o Android Studio
4. **Verifique versões**:
   - Android Studio: 2023.1+
   - Gradle: 8.2
   - Kotlin: 1.9.10
   - Compose: 1.5.4

## 🚀 Primeira Execução

1. **Abra o Android Studio**
2. **Open Project** → Selecione a pasta
3. **Aguarde sync** (pode demorar 5-10 min)
4. **Run** → Selecione emulador
5. **Aguarde instalação** no emulador
6. **Teste as funcionalidades!**

---

**🎉 Pronto! Sua IDE mobile está funcionando!** 

Agora você pode:
- ✅ Editar código Python/JS no celular
- ✅ Executar scripts offline
- ✅ Usar terminal integrado
- ✅ Salvar projetos localmente

**Happy Coding! 🚀📱**
