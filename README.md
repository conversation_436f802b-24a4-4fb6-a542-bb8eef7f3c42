# Visual Lab Studio IDE Mobile

Uma IDE mobile completa e premium para Android, desenvolvida com Jetpack Compose, totalmente offline e focada em produtividade para desenvolvedores.

## 🚀 Funcionalidades

### Editor de Código Offline
- Editor de texto com destaque de sintaxe para Python, JavaScript, HTML, CSS
- Fonte monoespaçada com numeração de linhas
- Modo dark ativado por padrão
- Suporte a múltiplas abas de arquivos

### Sistema de Arquivos Local (100% Offline)
- Abertura e salvamento com Storage Access Framework
- Navegação por estrutura de pastas do dispositivo
- Armazenamento local via Room (SQLite)
- Backup automático e projetos recentes

### Terminal Offline Embutido
- Terminal funcional com saída em tempo real
- Comandos locais (python, node, echo, ls, etc.)
- ProcessBuilder para execução real no sistema Android
- Interface com scroll, fundo preto, texto verde

### Execução de Código Local
- **Python**: Execução local com Chaquopy
- **JavaScript**: Execução com Rhino engine
- Output exibido no terminal embutido
- Suporte a bibliotecas pré-instaladas

### Interface Premium
- Layout responsivo para celular (Portrait)
- Navegação por abas (Editor, Terminal, Explorer, Config)
- Swipe horizontal entre abas
- Design dark com cores ciano/roxo neon
- Animações suaves com Compose Animation

### Banco de Dados Local
- SQLite via Room para:
  - Histórico de arquivos
  - Projetos salvos
  - Configurações do usuário
  - Snippets de código

### Configurações Personalizáveis
- Trocar tema (dark/light)
- Ajustar fonte e tamanho do editor
- Selecionar linguagem principal
- Auto-save e word wrap
- Atalhos de teclado

## 🛠️ Tecnologias Utilizadas

- **Kotlin** + **Jetpack Compose** - UI moderna e reativa
- **Room Database** - Armazenamento local
- **Chaquopy** - Execução Python offline
- **Rhino** - Engine JavaScript
- **Storage Access Framework** - Gerenciamento de arquivos
- **Coroutines** - Programação assíncrona
- **Material Design 3** - Design system

## 📱 Requisitos

- Android SDK 24+ (Android 7.0+)
- Arquitetura ARM64 ou x86_64
- 100MB de espaço livre
- Sem necessidade de internet

## 🏗️ Arquitetura

```
app/
├── src/main/java/com/visuallabstudio/ide/
│   ├── core/
│   │   ├── execution/          # Executores Python/JS
│   │   └── terminal/           # Gerenciador do terminal
│   ├── data/
│   │   ├── database/           # Room database
│   │   ├── entities/           # Entidades do banco
│   │   ├── dao/               # Data Access Objects
│   │   └── repository/        # Repositórios
│   ├── ui/
│   │   ├── components/        # Componentes reutilizáveis
│   │   ├── screens/           # Telas principais
│   │   └── theme/             # Tema e cores
│   ├── viewmodel/             # ViewModels
│   └── MainActivity.kt        # Activity principal
```

## 🎨 Design System

### Cores Principais
- **Background**: `#0D1117` (GitHub Dark)
- **Surface**: `#161B22`
- **Accent**: `#00D4FF` (Ciano)
- **Secondary**: `#8B5CF6` (Roxo)
- **Success**: `#39FF14` (Verde Neon)

### Syntax Highlighting
- **Keywords**: Roxo (`#8B5CF6`)
- **Strings**: Verde Neon (`#39FF14`)
- **Comments**: Cinza (`#7D8590`)
- **Numbers**: Ciano (`#00D4FF`)
- **Functions**: Dourado (`#FFD700`)

## 🚀 Como Executar

1. Clone o repositório
2. Abra no Android Studio
3. Sincronize as dependências Gradle
4. Execute no dispositivo ou emulador Android

```bash
git clone https://github.com/seu-usuario/visual-lab-studio-mobile
cd visual-lab-studio-mobile
./gradlew assembleDebug
```

## 📦 Build e Deploy

```bash
# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease

# Install on device
./gradlew installDebug
```

## 🔮 Roadmap Futuro

- [ ] Suporte a extensões/plugins
- [ ] Exportar projeto como ZIP
- [ ] Sincronização manual com GitHub
- [ ] Intellisense básico com IA local
- [ ] Suporte a mais linguagens (Kotlin, Java, C++)
- [ ] Debugger integrado
- [ ] Git integration
- [ ] Colaboração em tempo real

## 📄 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, leia o [CONTRIBUTING.md](CONTRIBUTING.md) para detalhes sobre nosso código de conduta e processo de submissão de pull requests.

## 📞 Suporte

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/seu-usuario/visual-lab-studio-mobile/issues)
- 📖 Documentação: [Wiki](https://github.com/seu-usuario/visual-lab-studio-mobile/wiki)

---

**Visual Lab Studio IDE Mobile** - Desenvolvendo o futuro da programação mobile! 🚀📱
