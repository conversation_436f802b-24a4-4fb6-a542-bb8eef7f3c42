// Visual Lab Studio IDE - JavaScript Example
// Este é um exemplo de código JavaScript que pode ser executado na IDE

function greet(name) {
    return `Olá, ${name}! Bem-vindo ao Visual Lab Studio IDE!`;
}

function calculateFactorial(n) {
    if (n <= 1) return 1;
    return n * calculateFactorial(n - 1);
}

function isPrime(num) {
    if (num <= 1) return false;
    if (num <= 3) return true;
    if (num % 2 === 0 || num % 3 === 0) return false;
    
    for (let i = 5; i * i <= num; i += 6) {
        if (num % i === 0 || num % (i + 2) === 0) {
            return false;
        }
    }
    return true;
}

function main() {
    console.log("=== Visual Lab Studio IDE - JavaScript Demo ===");
    console.log("");
    
    // Exemplo 1: Saudação
    const name = "Desenvolvedor";
    const message = greet(name);
    console.log(message);
    console.log("");
    
    // Exemplo 2: Fatorial
    const num = 5;
    const factorial = calculateFactorial(num);
    console.log(`Fatorial de ${num}: ${factorial}`);
    console.log("");
    
    // Exemplo 3: Números primos
    console.log("Números primos de 1 a 20:");
    const primes = [];
    for (let i = 1; i <= 20; i++) {
        if (isPrime(i)) {
            primes.push(i);
        }
    }
    console.log(primes.join(", "));
    console.log("");
    
    // Exemplo 4: Array operations
    const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    const squares = numbers.map(n => n * n);
    const evenSquares = squares.filter(n => n % 2 === 0);
    
    console.log("Números originais:", numbers);
    console.log("Quadrados:", squares);
    console.log("Quadrados pares:", evenSquares);
    console.log("");
    
    // Exemplo 5: Objeto e JSON
    const developer = {
        name: "João",
        age: 25,
        languages: ["JavaScript", "Python", "Kotlin"],
        isActive: true
    };
    
    console.log("Desenvolvedor:", JSON.stringify(developer, null, 2));
    console.log("");
    
    console.log("IDE funcionando perfeitamente! 🚀");
}

// Executar a função principal
main();
