// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        compose_version = '1.5.4'
        kotlin_version = '1.9.10'
        room_version = '2.6.0'
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.1.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.chaquo.python:gradle:14.0.2"
    }
}

plugins {
    id 'com.android.application' version '8.1.2' apply false
    id 'com.android.library' version '8.1.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.10' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
