package com.visuallabstudio.ide.data.dao

import androidx.room.*
import com.visuallabstudio.ide.data.entities.*
import kotlinx.coroutines.flow.Flow

@Dao
interface FileDao {
    @Query("SELECT * FROM files ORDER BY lastModified DESC")
    fun getAllFiles(): Flow<List<FileEntity>>

    @Query("SELECT * FROM files WHERE id = :id")
    suspend fun getFileById(id: String): FileEntity?

    @Query("SELECT * FROM files WHERE projectId = :projectId ORDER BY name ASC")
    fun getFilesByProject(projectId: String): Flow<List<FileEntity>>

    @Query("SELECT * FROM files WHERE language = :language ORDER BY lastModified DESC")
    fun getFilesByLanguage(language: String): Flow<List<FileEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertFile(file: FileEntity)

    @Update
    suspend fun updateFile(file: FileEntity)

    @Delete
    suspend fun deleteFile(file: FileEntity)

    @Query("DELETE FROM files WHERE id = :id")
    suspend fun deleteFileById(id: String)

    @Query("DELETE FROM files WHERE isTemporary = 1")
    suspend fun deleteTemporaryFiles()
}

@Dao
interface ProjectDao {
    @Query("SELECT * FROM projects ORDER BY lastOpened DESC")
    fun getAllProjects(): Flow<List<ProjectEntity>>

    @Query("SELECT * FROM projects WHERE id = :id")
    suspend fun getProjectById(id: String): ProjectEntity?

    @Query("SELECT * FROM projects WHERE isActive = 1 LIMIT 1")
    suspend fun getActiveProject(): ProjectEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProject(project: ProjectEntity)

    @Update
    suspend fun updateProject(project: ProjectEntity)

    @Delete
    suspend fun deleteProject(project: ProjectEntity)

    @Query("UPDATE projects SET isActive = 0")
    suspend fun deactivateAllProjects()

    @Query("UPDATE projects SET isActive = 1 WHERE id = :id")
    suspend fun setActiveProject(id: String)
}

@Dao
interface RecentFileDao {
    @Query("SELECT * FROM recent_files ORDER BY lastOpened DESC LIMIT 20")
    fun getRecentFiles(): Flow<List<RecentFileEntity>>

    @Query("SELECT * FROM recent_files WHERE fileId = :fileId")
    suspend fun getRecentFile(fileId: String): RecentFileEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecentFile(recentFile: RecentFileEntity)

    @Update
    suspend fun updateRecentFile(recentFile: RecentFileEntity)

    @Query("DELETE FROM recent_files WHERE fileId = :fileId")
    suspend fun deleteRecentFile(fileId: String)

    @Query("DELETE FROM recent_files")
    suspend fun clearRecentFiles()
}

@Dao
interface UserSettingsDao {
    @Query("SELECT * FROM user_settings")
    fun getAllSettings(): Flow<List<UserSettingsEntity>>

    @Query("SELECT * FROM user_settings WHERE key = :key")
    suspend fun getSetting(key: String): UserSettingsEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSetting(setting: UserSettingsEntity)

    @Query("DELETE FROM user_settings WHERE key = :key")
    suspend fun deleteSetting(key: String)
}

@Dao
interface CodeSnippetDao {
    @Query("SELECT * FROM code_snippets ORDER BY created DESC")
    fun getAllSnippets(): Flow<List<CodeSnippetEntity>>

    @Query("SELECT * FROM code_snippets WHERE language = :language ORDER BY created DESC")
    fun getSnippetsByLanguage(language: String): Flow<List<CodeSnippetEntity>>

    @Query("SELECT * FROM code_snippets WHERE isFavorite = 1 ORDER BY created DESC")
    fun getFavoriteSnippets(): Flow<List<CodeSnippetEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSnippet(snippet: CodeSnippetEntity)

    @Update
    suspend fun updateSnippet(snippet: CodeSnippetEntity)

    @Delete
    suspend fun deleteSnippet(snippet: CodeSnippetEntity)
}
