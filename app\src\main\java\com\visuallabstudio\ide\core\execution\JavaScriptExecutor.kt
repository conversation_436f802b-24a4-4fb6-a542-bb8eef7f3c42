package com.visuallabstudio.ide.core.execution

import android.content.Context
import kotlinx.coroutines.*
import org.mozilla.javascript.*
import java.io.StringWriter
import java.io.PrintWriter

class JavaScriptExecutor(private val context: Context) {
    
    private var rhinoContext: org.mozilla.javascript.Context? = null
    private var scope: Scriptable? = null
    
    init {
        initializeRhino()
    }
    
    private fun initializeRhino() {
        try {
            rhinoContext = org.mozilla.javascript.Context.enter()
            rhinoContext?.optimizationLevel = -1 // Disable optimization for Android
            scope = rhinoContext?.initStandardObjects()
            
            // Add console object for logging
            addConsoleObject()
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun addConsoleObject() {
        try {
            val console = rhinoContext?.newObject(scope)
            
            // Add console.log function
            val logFunction = object : BaseFunction() {
                override fun call(
                    cx: org.mozilla.javascript.Context?,
                    scope: Scriptable?,
                    thisObj: Scriptable?,
                    args: Array<out Any>?
                ): Any {
                    val message = args?.joinToString(" ") { it.toString() } ?: ""
                    outputCallback?.invoke(message)
                    return org.mozilla.javascript.Context.getUndefinedValue()
                }
            }
            
            console?.put("log", console, logFunction)
            console?.put("info", console, logFunction)
            console?.put("warn", console, logFunction)
            console?.put("error", console, logFunction)
            
            scope?.put("console", scope, console)
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private var outputCallback: ((String) -> Unit)? = null
    
    suspend fun executeCode(
        code: String,
        onOutput: (String) -> Unit,
        onError: (String) -> Unit,
        onComplete: () -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            val ctx = rhinoContext ?: run {
                onError("JavaScript engine not initialized")
                onComplete()
                return@withContext
            }
            
            val currentScope = scope ?: run {
                onError("JavaScript scope not initialized")
                onComplete()
                return@withContext
            }
            
            outputCallback = onOutput
            
            try {
                val result = ctx.evaluateString(currentScope, code, "<script>", 1, null)
                
                // If result is not undefined, output it
                if (result != org.mozilla.javascript.Context.getUndefinedValue()) {
                    onOutput(org.mozilla.javascript.Context.toString(result))
                }
                
            } catch (e: RhinoException) {
                onError("JavaScript error: ${e.message}")
            } catch (e: Exception) {
                onError("Execution error: ${e.message}")
            } finally {
                outputCallback = null
                onComplete()
            }
            
        } catch (e: Exception) {
            onError("Failed to execute JavaScript code: ${e.message}")
            onComplete()
        }
    }
    
    suspend fun executeFile(
        filePath: String,
        onOutput: (String) -> Unit,
        onError: (String) -> Unit,
        onComplete: () -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            val file = java.io.File(filePath)
            if (!file.exists()) {
                onError("File not found: $filePath")
                onComplete()
                return@withContext
            }
            
            val code = file.readText()
            executeCode(code, onOutput, onError, onComplete)
            
        } catch (e: Exception) {
            onError("Failed to execute JavaScript file: ${e.message}")
            onComplete()
        }
    }
    
    suspend fun evaluateExpression(
        expression: String,
        onResult: (String) -> Unit,
        onError: (String) -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            val ctx = rhinoContext ?: run {
                onError("JavaScript engine not initialized")
                return@withContext
            }
            
            val currentScope = scope ?: run {
                onError("JavaScript scope not initialized")
                return@withContext
            }
            
            val result = ctx.evaluateString(currentScope, expression, "<expression>", 1, null)
            onResult(org.mozilla.javascript.Context.toString(result))
            
        } catch (e: RhinoException) {
            onError("JavaScript error: ${e.message}")
        } catch (e: Exception) {
            onError("Evaluation error: ${e.message}")
        }
    }
    
    fun addGlobalFunction(name: String, function: (Array<Any>) -> Any) {
        try {
            val ctx = rhinoContext ?: return
            val currentScope = scope ?: return
            
            val jsFunction = object : BaseFunction() {
                override fun call(
                    cx: org.mozilla.javascript.Context?,
                    scope: Scriptable?,
                    thisObj: Scriptable?,
                    args: Array<out Any>?
                ): Any {
                    return try {
                        function(args ?: emptyArray())
                    } catch (e: Exception) {
                        throw WrappedException(e)
                    }
                }
            }
            
            currentScope.put(name, currentScope, jsFunction)
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun addGlobalVariable(name: String, value: Any) {
        try {
            val currentScope = scope ?: return
            currentScope.put(name, currentScope, value)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun getEngineVersion(): String {
        return try {
            "Rhino ${org.mozilla.javascript.Context.jsVersionToString(org.mozilla.javascript.Context.VERSION_DEFAULT)}"
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    fun resetScope() {
        try {
            scope = rhinoContext?.initStandardObjects()
            addConsoleObject()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun cleanup() {
        try {
            outputCallback = null
            org.mozilla.javascript.Context.exit()
            rhinoContext = null
            scope = null
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
