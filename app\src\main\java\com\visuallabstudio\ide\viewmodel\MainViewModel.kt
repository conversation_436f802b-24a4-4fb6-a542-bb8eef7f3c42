package com.visuallabstudio.ide.viewmodel

import android.app.Application
import android.net.Uri
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.visuallabstudio.ide.core.execution.JavaScriptExecutor
import com.visuallabstudio.ide.core.execution.PythonExecutor
import com.visuallabstudio.ide.core.terminal.TerminalManager
import com.visuallabstudio.ide.data.database.AppDatabase
import com.visuallabstudio.ide.data.entities.FileEntity
import com.visuallabstudio.ide.data.entities.RecentFileEntity
import com.visuallabstudio.ide.data.repository.FileRepository
import com.visuallabstudio.ide.data.repository.SettingsRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*

class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val database = AppDatabase.getDatabase(application)
    private val fileRepository = FileRepository(
        context = application,
        fileDao = database.fileDao(),
        recentFileDao = database.recentFileDao(),
        projectDao = database.projectDao()
    )
    private val settingsRepository = SettingsRepository(database.userSettingsDao())
    
    private val pythonExecutor = PythonExecutor(application)
    private val jsExecutor = JavaScriptExecutor(application)
    
    private val _terminalManager = MutableStateFlow<TerminalManager?>(null)
    val terminalManager: StateFlow<TerminalManager?> = _terminalManager.asStateFlow()
    
    private val _currentFile = MutableStateFlow<FileEntity?>(null)
    val currentFile: StateFlow<FileEntity?> = _currentFile.asStateFlow()
    
    private val _openFiles = MutableStateFlow<List<FileEntity>>(emptyList())
    val openFiles: StateFlow<List<FileEntity>> = _openFiles.asStateFlow()
    
    // Settings
    private val _isDarkTheme = MutableStateFlow(true)
    val isDarkTheme: StateFlow<Boolean> = _isDarkTheme.asStateFlow()
    
    private val _fontSize = MutableStateFlow(14)
    val fontSize: StateFlow<Int> = _fontSize.asStateFlow()
    
    private val _isAutoSaveEnabled = MutableStateFlow(true)
    val isAutoSaveEnabled: StateFlow<Boolean> = _isAutoSaveEnabled.asStateFlow()
    
    private val _areLineNumbersEnabled = MutableStateFlow(true)
    val areLineNumbersEnabled: StateFlow<Boolean> = _areLineNumbersEnabled.asStateFlow()
    
    private val _isSyntaxHighlightEnabled = MutableStateFlow(true)
    val isSyntaxHighlightEnabled: StateFlow<Boolean> = _isSyntaxHighlightEnabled.asStateFlow()
    
    private val _defaultLanguage = MutableStateFlow("python")
    val defaultLanguage: StateFlow<String> = _defaultLanguage.asStateFlow()
    
    // File data
    val allFiles: StateFlow<List<FileEntity>> = fileRepository.getAllFiles()
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())
    
    val recentFiles: StateFlow<List<RecentFileEntity>> = fileRepository.getRecentFiles()
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())
    
    init {
        initializeTerminal()
        loadSettings()
    }
    
    private fun initializeTerminal() {
        _terminalManager.value = TerminalManager(getApplication())
    }
    
    private fun loadSettings() {
        viewModelScope.launch {
            _isDarkTheme.value = settingsRepository.getTheme() == "dark"
            _fontSize.value = settingsRepository.getFontSize()
            _isAutoSaveEnabled.value = settingsRepository.isAutoSaveEnabled()
            _areLineNumbersEnabled.value = settingsRepository.areLineNumbersEnabled()
            _isSyntaxHighlightEnabled.value = settingsRepository.isSyntaxHighlightEnabled()
            _defaultLanguage.value = settingsRepository.getDefaultLanguage()
        }
    }
    
    fun createNewFile() {
        viewModelScope.launch {
            val newFile = fileRepository.createNewFile(
                name = "Untitled.${getFileExtension(_defaultLanguage.value)}",
                content = getDefaultContent(_defaultLanguage.value)
            )
            addToOpenFiles(newFile)
            _currentFile.value = newFile
        }
    }
    
    fun openFile(uri: Uri) {
        viewModelScope.launch {
            val file = fileRepository.openFile(uri)
            file?.let {
                addToOpenFiles(it)
                _currentFile.value = it
            }
        }
    }
    
    fun openFileById(fileId: String) {
        viewModelScope.launch {
            val file = fileRepository.getFileById(fileId)
            file?.let {
                addToOpenFiles(it)
                _currentFile.value = it
            }
        }
    }
    
    fun saveFile(file: FileEntity, uri: Uri? = null) {
        viewModelScope.launch {
            val success = fileRepository.saveFile(file, uri)
            if (success) {
                updateOpenFile(file)
                if (_currentFile.value?.id == file.id) {
                    _currentFile.value = file
                }
            }
        }
    }
    
    fun selectFile(fileId: String) {
        val file = _openFiles.value.find { it.id == fileId }
        file?.let {
            _currentFile.value = it
        }
    }
    
    fun closeFile(fileId: String) {
        val currentOpenFiles = _openFiles.value.toMutableList()
        val fileIndex = currentOpenFiles.indexOfFirst { it.id == fileId }
        
        if (fileIndex != -1) {
            currentOpenFiles.removeAt(fileIndex)
            _openFiles.value = currentOpenFiles
            
            // If closing current file, select another one or null
            if (_currentFile.value?.id == fileId) {
                _currentFile.value = when {
                    currentOpenFiles.isEmpty() -> null
                    fileIndex < currentOpenFiles.size -> currentOpenFiles[fileIndex]
                    else -> currentOpenFiles.lastOrNull()
                }
            }
        }
    }
    
    fun updateFileContent(fileId: String, content: String) {
        viewModelScope.launch {
            fileRepository.updateFileContent(fileId, content)
            
            // Update in open files list
            val updatedFiles = _openFiles.value.map { file ->
                if (file.id == fileId) {
                    file.copy(content = content, lastModified = Date())
                } else {
                    file
                }
            }
            _openFiles.value = updatedFiles
            
            // Update current file if it's the one being edited
            if (_currentFile.value?.id == fileId) {
                _currentFile.value = _currentFile.value?.copy(
                    content = content,
                    lastModified = Date()
                )
            }
        }
    }
    
    fun deleteFile(fileId: String) {
        viewModelScope.launch {
            fileRepository.deleteFile(fileId)
            closeFile(fileId)
        }
    }
    
    fun executeCode(code: String, language: String) {
        val terminal = _terminalManager.value ?: return
        
        when (language.lowercase()) {
            "python" -> {
                viewModelScope.launch {
                    pythonExecutor.executeCode(
                        code = code,
                        onOutput = { output ->
                            // Terminal will handle output display
                        },
                        onError = { error ->
                            // Terminal will handle error display
                        },
                        onComplete = {
                            // Execution completed
                        }
                    )
                }
            }
            "javascript", "js" -> {
                viewModelScope.launch {
                    jsExecutor.executeCode(
                        code = code,
                        onOutput = { output ->
                            // Terminal will handle output display
                        },
                        onError = { error ->
                            // Terminal will handle error display
                        },
                        onComplete = {
                            // Execution completed
                        }
                    )
                }
            }
            else -> {
                terminal.executeCommand("echo 'Execution not supported for $language'")
            }
        }
    }
    
    // Settings methods
    fun setDarkTheme(isDark: Boolean) {
        _isDarkTheme.value = isDark
        viewModelScope.launch {
            settingsRepository.setTheme(if (isDark) "dark" else "light")
        }
    }
    
    fun setFontSize(size: Int) {
        _fontSize.value = size
        viewModelScope.launch {
            settingsRepository.setFontSize(size)
        }
    }
    
    fun setAutoSave(enabled: Boolean) {
        _isAutoSaveEnabled.value = enabled
        viewModelScope.launch {
            settingsRepository.setAutoSave(enabled)
        }
    }
    
    fun setLineNumbers(enabled: Boolean) {
        _areLineNumbersEnabled.value = enabled
        viewModelScope.launch {
            settingsRepository.setLineNumbers(enabled)
        }
    }
    
    fun setSyntaxHighlight(enabled: Boolean) {
        _isSyntaxHighlightEnabled.value = enabled
        viewModelScope.launch {
            settingsRepository.setSyntaxHighlight(enabled)
        }
    }
    
    fun setDefaultLanguage(language: String) {
        _defaultLanguage.value = language
        viewModelScope.launch {
            settingsRepository.setDefaultLanguage(language)
        }
    }
    
    private fun addToOpenFiles(file: FileEntity) {
        val currentFiles = _openFiles.value.toMutableList()
        if (!currentFiles.any { it.id == file.id }) {
            currentFiles.add(file)
            _openFiles.value = currentFiles
        }
    }
    
    private fun updateOpenFile(file: FileEntity) {
        val updatedFiles = _openFiles.value.map { openFile ->
            if (openFile.id == file.id) file else openFile
        }
        _openFiles.value = updatedFiles
    }
    
    private fun getFileExtension(language: String): String {
        return when (language.lowercase()) {
            "python" -> "py"
            "javascript" -> "js"
            "html" -> "html"
            "css" -> "css"
            "json" -> "json"
            else -> "txt"
        }
    }
    
    private fun getDefaultContent(language: String): String {
        return when (language.lowercase()) {
            "python" -> "# Welcome to Visual Lab Studio IDE\nprint(\"Hello, World!\")\n"
            "javascript" -> "// Welcome to Visual Lab Studio IDE\nconsole.log(\"Hello, World!\");\n"
            "html" -> "<!DOCTYPE html>\n<html>\n<head>\n    <title>Hello World</title>\n</head>\n<body>\n    <h1>Hello, World!</h1>\n</body>\n</html>\n"
            "css" -> "/* Welcome to Visual Lab Studio IDE */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n"
            else -> "Welcome to Visual Lab Studio IDE\n"
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        _terminalManager.value?.cleanup()
        pythonExecutor.cleanup()
        jsExecutor.cleanup()
    }
}
