package com.visuallabstudio.ide.data.repository

import android.content.Context
import android.net.Uri
import androidx.documentfile.provider.DocumentFile
import com.visuallabstudio.ide.data.dao.*
import com.visuallabstudio.ide.data.entities.*
import kotlinx.coroutines.flow.Flow
import java.io.*
import java.util.*

class FileRepository(
    private val context: Context,
    private val fileDao: FileDao,
    private val recentFileDao: RecentFileDao,
    private val projectDao: ProjectDao
) {
    
    suspend fun openFile(uri: Uri): FileEntity? {
        return try {
            val documentFile = DocumentFile.fromSingleUri(context, uri)
            documentFile?.let { doc ->
                val inputStream = context.contentResolver.openInputStream(uri)
                val content = inputStream?.bufferedReader()?.use { it.readText() } ?: ""
                
                val fileEntity = FileEntity(
                    id = UUID.randomUUID().toString(),
                    name = doc.name ?: "Untitled",
                    path = uri.toString(),
                    content = content,
                    language = getLanguageFromExtension(doc.name ?: ""),
                    size = doc.length(),
                    lastModified = Date(doc.lastModified()),
                    isTemporary = false
                )
                
                fileDao.insertFile(fileEntity)
                addToRecentFiles(fileEntity)
                fileEntity
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    suspend fun saveFile(fileEntity: FileEntity, uri: Uri? = null): Boolean {
        return try {
            val targetUri = uri ?: Uri.parse(fileEntity.path)
            val outputStream = context.contentResolver.openOutputStream(targetUri)
            outputStream?.use { stream ->
                stream.write(fileEntity.content.toByteArray())
            }
            
            val updatedFile = fileEntity.copy(
                lastModified = Date(),
                size = fileEntity.content.length.toLong()
            )
            fileDao.updateFile(updatedFile)
            addToRecentFiles(updatedFile)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    suspend fun createNewFile(name: String, content: String = ""): FileEntity {
        val fileEntity = FileEntity(
            id = UUID.randomUUID().toString(),
            name = name,
            path = "", // Will be set when saved
            content = content,
            language = getLanguageFromExtension(name),
            size = content.length.toLong(),
            lastModified = Date(),
            isTemporary = true
        )
        
        fileDao.insertFile(fileEntity)
        return fileEntity
    }
    
    suspend fun updateFileContent(fileId: String, content: String): Boolean {
        return try {
            val file = fileDao.getFileById(fileId)
            file?.let {
                val updatedFile = it.copy(
                    content = content,
                    size = content.length.toLong(),
                    lastModified = Date()
                )
                fileDao.updateFile(updatedFile)
                true
            } ?: false
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    private suspend fun addToRecentFiles(fileEntity: FileEntity) {
        val existing = recentFileDao.getRecentFile(fileEntity.id)
        if (existing != null) {
            recentFileDao.updateRecentFile(
                existing.copy(
                    lastOpened = Date(),
                    openCount = existing.openCount + 1
                )
            )
        } else {
            recentFileDao.insertRecentFile(
                RecentFileEntity(
                    id = UUID.randomUUID().toString(),
                    fileId = fileEntity.id,
                    fileName = fileEntity.name,
                    filePath = fileEntity.path,
                    lastOpened = Date()
                )
            )
        }
    }
    
    private fun getLanguageFromExtension(fileName: String): String {
        return when (fileName.substringAfterLast('.', "").lowercase()) {
            "py" -> "python"
            "js" -> "javascript"
            "html", "htm" -> "html"
            "css" -> "css"
            "json" -> "json"
            "xml" -> "xml"
            "kt" -> "kotlin"
            "java" -> "java"
            "cpp", "c" -> "cpp"
            "md" -> "markdown"
            else -> "text"
        }
    }
    
    fun getAllFiles(): Flow<List<FileEntity>> = fileDao.getAllFiles()
    fun getRecentFiles(): Flow<List<RecentFileEntity>> = recentFileDao.getRecentFiles()
    suspend fun getFileById(id: String): FileEntity? = fileDao.getFileById(id)
    suspend fun deleteFile(fileId: String) = fileDao.deleteFileById(fileId)
}
