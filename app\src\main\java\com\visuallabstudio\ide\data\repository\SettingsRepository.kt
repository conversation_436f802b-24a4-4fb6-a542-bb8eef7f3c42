package com.visuallabstudio.ide.data.repository

import com.visuallabstudio.ide.data.dao.UserSettingsDao
import com.visuallabstudio.ide.data.entities.UserSettingsEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class SettingsRepository(private val settingsDao: UserSettingsDao) {
    
    companion object {
        const val THEME_KEY = "theme"
        const val FONT_SIZE_KEY = "font_size"
        const val EDITOR_FONT_KEY = "editor_font"
        const val AUTO_SAVE_KEY = "auto_save"
        const val WORD_WRAP_KEY = "word_wrap"
        const val LINE_NUMBERS_KEY = "line_numbers"
        const val SYNTAX_HIGHLIGHT_KEY = "syntax_highlight"
        const val DEFAULT_LANGUAGE_KEY = "default_language"
        
        // Default values
        const val DEFAULT_THEME = "dark"
        const val DEFAULT_FONT_SIZE = "14"
        const val DEFAULT_EDITOR_FONT = "monospace"
        const val DEFAULT_AUTO_SAVE = "true"
        const val DEFAULT_WORD_WRAP = "false"
        const val DEFAULT_LINE_NUMBERS = "true"
        const val DEFAULT_SYNTAX_HIGHLIGHT = "true"
        const val DEFAULT_LANGUAGE = "python"
    }
    
    suspend fun getString(key: String, defaultValue: String): String {
        return settingsDao.getSetting(key)?.value ?: defaultValue
    }
    
    suspend fun getBoolean(key: String, defaultValue: Boolean): Boolean {
        return settingsDao.getSetting(key)?.value?.toBoolean() ?: defaultValue
    }
    
    suspend fun getInt(key: String, defaultValue: Int): Int {
        return settingsDao.getSetting(key)?.value?.toIntOrNull() ?: defaultValue
    }
    
    suspend fun getFloat(key: String, defaultValue: Float): Float {
        return settingsDao.getSetting(key)?.value?.toFloatOrNull() ?: defaultValue
    }
    
    suspend fun setString(key: String, value: String) {
        settingsDao.insertSetting(
            UserSettingsEntity(key = key, value = value, type = "string")
        )
    }
    
    suspend fun setBoolean(key: String, value: Boolean) {
        settingsDao.insertSetting(
            UserSettingsEntity(key = key, value = value.toString(), type = "boolean")
        )
    }
    
    suspend fun setInt(key: String, value: Int) {
        settingsDao.insertSetting(
            UserSettingsEntity(key = key, value = value.toString(), type = "int")
        )
    }
    
    suspend fun setFloat(key: String, value: Float) {
        settingsDao.insertSetting(
            UserSettingsEntity(key = key, value = value.toString(), type = "float")
        )
    }
    
    suspend fun deleteSetting(key: String) {
        settingsDao.deleteSetting(key)
    }
    
    fun getAllSettings(): Flow<List<UserSettingsEntity>> = settingsDao.getAllSettings()
    
    // Convenience methods for common settings
    suspend fun getTheme(): String = getString(THEME_KEY, DEFAULT_THEME)
    suspend fun setTheme(theme: String) = setString(THEME_KEY, theme)
    
    suspend fun getFontSize(): Int = getInt(FONT_SIZE_KEY, DEFAULT_FONT_SIZE.toInt())
    suspend fun setFontSize(size: Int) = setInt(FONT_SIZE_KEY, size)
    
    suspend fun getEditorFont(): String = getString(EDITOR_FONT_KEY, DEFAULT_EDITOR_FONT)
    suspend fun setEditorFont(font: String) = setString(EDITOR_FONT_KEY, font)
    
    suspend fun isAutoSaveEnabled(): Boolean = getBoolean(AUTO_SAVE_KEY, DEFAULT_AUTO_SAVE.toBoolean())
    suspend fun setAutoSave(enabled: Boolean) = setBoolean(AUTO_SAVE_KEY, enabled)
    
    suspend fun isWordWrapEnabled(): Boolean = getBoolean(WORD_WRAP_KEY, DEFAULT_WORD_WRAP.toBoolean())
    suspend fun setWordWrap(enabled: Boolean) = setBoolean(WORD_WRAP_KEY, enabled)
    
    suspend fun areLineNumbersEnabled(): Boolean = getBoolean(LINE_NUMBERS_KEY, DEFAULT_LINE_NUMBERS.toBoolean())
    suspend fun setLineNumbers(enabled: Boolean) = setBoolean(LINE_NUMBERS_KEY, enabled)
    
    suspend fun isSyntaxHighlightEnabled(): Boolean = getBoolean(SYNTAX_HIGHLIGHT_KEY, DEFAULT_SYNTAX_HIGHLIGHT.toBoolean())
    suspend fun setSyntaxHighlight(enabled: Boolean) = setBoolean(SYNTAX_HIGHLIGHT_KEY, enabled)
    
    suspend fun getDefaultLanguage(): String = getString(DEFAULT_LANGUAGE_KEY, DEFAULT_LANGUAGE)
    suspend fun setDefaultLanguage(language: String) = setString(DEFAULT_LANGUAGE_KEY, language)
}
