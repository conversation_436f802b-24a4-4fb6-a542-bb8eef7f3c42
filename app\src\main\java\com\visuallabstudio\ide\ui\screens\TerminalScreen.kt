package com.visuallabstudio.ide.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.visuallabstudio.ide.ui.components.Terminal
import com.visuallabstudio.ide.viewmodel.MainViewModel

@Composable
fun TerminalScreen(
    viewModel: MainViewModel
) {
    val terminalManager by viewModel.terminalManager.collectAsState()
    
    terminalManager?.let { manager ->
        Terminal(
            terminalManager = manager,
            modifier = Modifier.fillMaxSize()
        )
    }
}
