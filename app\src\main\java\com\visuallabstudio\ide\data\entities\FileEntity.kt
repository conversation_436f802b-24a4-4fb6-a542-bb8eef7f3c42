package com.visuallabstudio.ide.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "files")
data class FileEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val path: String,
    val content: String,
    val language: String, // py, js, html, css, txt
    val size: Long,
    val lastModified: Date,
    val isTemporary: Boolean = false,
    val projectId: String? = null
)

@Entity(tableName = "projects")
data class ProjectEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val path: String,
    val description: String? = null,
    val language: String, // main language
    val created: Date,
    val lastOpened: Date,
    val isActive: Boolean = false
)

@Entity(tableName = "recent_files")
data class RecentFileEntity(
    @PrimaryKey
    val id: String,
    val fileId: String,
    val fileName: String,
    val filePath: String,
    val lastOpened: Date,
    val openCount: Int = 1
)

@Entity(tableName = "user_settings")
data class UserSettingsEntity(
    @PrimaryKey
    val key: String,
    val value: String,
    val type: String // string, boolean, int, float
)

@Entity(tableName = "code_snippets")
data class CodeSnippetEntity(
    @PrimaryKey
    val id: String,
    val title: String,
    val code: String,
    val language: String,
    val description: String? = null,
    val tags: String? = null, // comma separated
    val created: Date,
    val isFavorite: Boolean = false
)
