# Visual Lab Studio IDE - Python Example
# Este é um exemplo de código Python que pode ser executado na IDE

def greet(name):
    """Função para cumprimentar uma pessoa"""
    return f"Ol<PERSON>, {name}! Bem-vindo ao Visual Lab Studio IDE!"

def calculate_fibonacci(n):
    """Calcula a sequência de Fibonacci até n termos"""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib = [0, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    
    return fib

def main():
    print("=== Visual Lab Studio IDE - Python Demo ===")
    print()
    
    # Exemplo 1: Saudação
    name = "Desenvolvedor"
    message = greet(name)
    print(message)
    print()
    
    # Exemplo 2: Fibonacci
    print("Sequência de Fibonacci (10 termos):")
    fib_sequence = calculate_fibonacci(10)
    print(fib_sequence)
    print()
    
    # Exemplo 3: Operações matemáticas
    print("Operações matemáticas:")
    a, b = 15, 7
    print(f"{a} + {b} = {a + b}")
    print(f"{a} - {b} = {a - b}")
    print(f"{a} * {b} = {a * b}")
    print(f"{a} / {b} = {a / b:.2f}")
    print()
    
    # Exemplo 4: Lista e loops
    print("Números pares de 1 a 20:")
    even_numbers = [i for i in range(1, 21) if i % 2 == 0]
    print(even_numbers)
    print()
    
    print("IDE funcionando perfeitamente! 🚀")

if __name__ == "__main__":
    main()
